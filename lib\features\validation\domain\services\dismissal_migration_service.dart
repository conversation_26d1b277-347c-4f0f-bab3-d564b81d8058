import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';

/// Service to migrate existing multiple dismissal entries to single entries
class DismissalMigrationService {
  final MeterReadingRepository _meterReadingRepository;

  /// Constructor
  DismissalMigrationService(this._meterReadingRepository);

  /// Migrate existing multiple dismissal entries to single entries
  Future<void> migrateExistingDismissalEntries() async {
    try {
      Logger.info('DismissalMigrationService: Starting migration of dismissal entries');

      // Get all dismissal entries
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final dismissalEntries = allReadings
          .where((reading) => reading.status == EntryStatus.ignored)
          .toList();

      if (dismissalEntries.isEmpty) {
        Logger.info('DismissalMigrationService: No dismissal entries found to migrate');
        return;
      }

      Logger.info('DismissalMigrationService: Found ${dismissalEntries.length} dismissal entries');

      // Group dismissal entries by their gap periods
      final dismissalGroups = await _groupDismissalEntriesByGap(dismissalEntries, allReadings);

      Logger.info('DismissalMigrationService: Found ${dismissalGroups.length} dismissal groups');

      int migratedCount = 0;
      for (final group in dismissalGroups) {
        final migrated = await _migrateDismissalGroup(group);
        if (migrated) {
          migratedCount++;
        }
      }

      Logger.info('DismissalMigrationService: Successfully migrated $migratedCount dismissal groups');
    } catch (e) {
      Logger.error('DismissalMigrationService: Error during migration: $e');
      rethrow;
    }
  }

  /// Group dismissal entries by their gap periods
  Future<List<List<MeterReading>>> _groupDismissalEntriesByGap(
    List<MeterReading> dismissalEntries,
    List<MeterReading> allReadings,
  ) async {
    final groups = <List<MeterReading>>[];
    final processedEntries = <int>{};

    // Get non-dismissal entries for gap detection
    final realEntries = allReadings
        .where((reading) => reading.status != EntryStatus.ignored)
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));

    for (final dismissalEntry in dismissalEntries) {
      if (processedEntries.contains(dismissalEntry.id)) {
        continue;
      }

      // Find the gap this dismissal entry belongs to
      final gap = _findGapForDismissalEntry(dismissalEntry, realEntries);
      if (gap == null) {
        continue;
      }

      // Find all dismissal entries in this gap
      final groupEntries = dismissalEntries
          .where((entry) =>
              entry.date.isAfter(gap.start) &&
              entry.date.isBefore(gap.end) &&
              !processedEntries.contains(entry.id))
          .toList();

      if (groupEntries.isNotEmpty) {
        groups.add(groupEntries);
        for (final entry in groupEntries) {
          processedEntries.add(entry.id!);
        }
      }
    }

    return groups;
  }

  /// Find the gap period for a dismissal entry
  _GapPeriod? _findGapForDismissalEntry(
    MeterReading dismissalEntry,
    List<MeterReading> realEntries,
  ) {
    DateTime? previousEntry;
    DateTime? nextEntry;

    for (int i = 0; i < realEntries.length; i++) {
      if (realEntries[i].date.isAfter(dismissalEntry.date)) {
        nextEntry = realEntries[i].date;
        if (i > 0) {
          previousEntry = realEntries[i - 1].date;
        }
        break;
      }
    }

    if (previousEntry != null && nextEntry != null) {
      return _GapPeriod(previousEntry, nextEntry);
    }

    return null;
  }

  /// Migrate a group of dismissal entries to a single entry
  Future<bool> _migrateDismissalGroup(List<MeterReading> groupEntries) async {
    try {
      if (groupEntries.isEmpty) {
        return false;
      }

      // Sort entries by date
      groupEntries.sort((a, b) => a.date.compareTo(b.date));

      // Parse gap information from the first entry's notes
      final firstEntry = groupEntries.first;
      final gapInfo = _parseGapInfoFromNotes(firstEntry.notes);
      if (gapInfo == null) {
        Logger.warning('DismissalMigrationService: Could not parse gap info from notes: ${firstEntry.notes}');
        return false;
      }

      // Calculate new dismissal date (start + 62 days)
      final newDismissalDate = gapInfo.start.add(const Duration(days: 62));

      // Check if new date would be valid (before end date)
      if (newDismissalDate.isAfter(gapInfo.end) || newDismissalDate.isAtSameMomentAs(gapInfo.end)) {
        Logger.warning('DismissalMigrationService: Gap too short for new dismissal entry');
        // Delete all entries in this group since gap is too short
        for (final entry in groupEntries) {
          if (entry.id != null) {
            await _meterReadingRepository.deleteMeterReading(entry.id!);
          }
        }
        return true;
      }

      // Create new single dismissal entry
      final newDismissalEntry = MeterReading(
        value: 0.0,
        date: newDismissalDate,
        status: EntryStatus.ignored,
        notes: firstEntry.notes, // Preserve original notes
      );

      // Add new entry
      await _meterReadingRepository.addMeterReading(newDismissalEntry);

      // Delete old entries
      for (final entry in groupEntries) {
        if (entry.id != null) {
          await _meterReadingRepository.deleteMeterReading(entry.id!);
        }
      }

      Logger.info('DismissalMigrationService: Migrated ${groupEntries.length} entries to 1 entry for gap ${gapInfo.start} - ${gapInfo.end}');
      return true;
    } catch (e) {
      Logger.error('DismissalMigrationService: Error migrating group: $e');
      return false;
    }
  }

  /// Parse gap information from dismissal entry notes
  _GapInfo? _parseGapInfoFromNotes(String? notes) {
    if (notes == null) return null;

    // Expected format: "Dismissed missing entry gap: X days (DD/MM/YYYY - DD/MM/YYYY)"
    final regex = RegExp(r'Dismissed missing entry gap: (\d+) days \((\d{1,2}/\d{1,2}/\d{4}) - (\d{1,2}/\d{1,2}/\d{4})\)');
    final match = regex.firstMatch(notes);

    if (match != null) {
      try {
        final gapDays = int.parse(match.group(1)!);
        final startDateStr = match.group(2)!;
        final endDateStr = match.group(3)!;

        final startDate = _parseDate(startDateStr);
        final endDate = _parseDate(endDateStr);

        if (startDate != null && endDate != null) {
          return _GapInfo(startDate, endDate, gapDays);
        }
      } catch (e) {
        Logger.error('DismissalMigrationService: Error parsing dates from notes: $e');
      }
    }

    return null;
  }

  /// Parse date from DD/MM/YYYY format
  DateTime? _parseDate(String dateStr) {
    try {
      final parts = dateStr.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      Logger.error('DismissalMigrationService: Error parsing date: $dateStr');
    }
    return null;
  }
}

/// Helper class for gap period
class _GapPeriod {
  final DateTime start;
  final DateTime end;

  _GapPeriod(this.start, this.end);
}

/// Helper class for gap information
class _GapInfo {
  final DateTime start;
  final DateTime end;
  final int gapDays;

  _GapInfo(this.start, this.end, this.gapDays);
}
