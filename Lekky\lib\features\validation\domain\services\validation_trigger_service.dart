import '../../../../core/utils/logger.dart';
import '../../../../core/utils/bulk_operation_context.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import 'data_integrity_service.dart';
import 'meter_reading_validator.dart';
import 'dismissed_entry_service.dart';

/// Service for triggering validation after data operations
class ValidationTriggerService {
  final DataIntegrityService _dataIntegrityService;
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final MeterReadingValidator _validator;
  final logger = Logger('ValidationTriggerService');

  /// Constructor
  ValidationTriggerService({
    required DataIntegrityService dataIntegrityService,
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
    required MeterReadingValidator validator,
  })  : _dataIntegrityService = dataIntegrityService,
        _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository,
        _validator = validator;

  /// Trigger validation after adding a meter reading
  Future<void> validateAfterAdd(int meterReadingId) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final meterReading =
          await _meterReadingRepository.getMeterReadingById(meterReadingId);
      if (meterReading != null) {
        await _validateAndUpdateSingleReading(meterReading);
      }
    } catch (e) {
      logger.e('Error validating after add', details: e.toString());
    }
  }

  /// Trigger validation after updating a meter reading
  Future<void> validateAfterUpdate(int meterReadingId) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final meterReading =
          await _meterReadingRepository.getMeterReadingById(meterReadingId);
      if (meterReading != null) {
        await _validateAndUpdateSingleReading(meterReading);

        // Also validate potentially affected readings (next reading in sequence)
        await _validateAffectedReadings(meterReading);

        // Clean up surplus dismissal entries during validation cycle
        await _cleanupSurplusDismissalEntries();
      }
    } catch (e) {
      logger.e('Error validating after update', details: e.toString());
    }
  }

  /// Trigger validation after deleting a meter reading
  Future<void> validateAfterDelete(DateTime deletedReadingDate) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      // Find the next reading after the deleted one and validate it
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final sortedReadings = List<MeterReading>.from(allReadings)
        ..sort((a, b) => a.date.compareTo(b.date));

      // Find the first reading after the deleted date
      final nextReading = sortedReadings
          .where((reading) => reading.date.isAfter(deletedReadingDate))
          .firstOrNull;

      if (nextReading != null) {
        await _validateAndUpdateSingleReading(nextReading);
      }
    } catch (e) {
      logger.e('Error validating after delete', details: e.toString());
    }
  }

  /// Trigger validation after import operations
  Future<void> validateAfterImport() async {
    try {
      await _dataIntegrityService.validateAndUpdateAllMeterReadings();
      await _dataIntegrityService.validateAndUpdateAllTopUps();
      logger.i('Validation completed after import');
    } catch (e) {
      logger.e('Error validating after import', details: e.toString());
    }
  }

  /// Trigger validation after adding a top-up
  Future<void> validateAfterTopUpAdd(int topUpId) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final topUp = await _topUpRepository.getTopUpById(topUpId);
      if (topUp != null) {
        await _validateAndUpdateSingleTopUp(topUp);
        // Validate all subsequent meter readings that might be affected
        await _validateSubsequentReadings(topUp.date);
      }
    } catch (e) {
      logger.e('Error validating after top-up add', details: e.toString());
    }
  }

  /// Trigger validation after updating a top-up
  Future<void> validateAfterTopUpUpdate(int topUpId) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final topUp = await _topUpRepository.getTopUpById(topUpId);
      if (topUp != null) {
        await _validateAndUpdateSingleTopUp(topUp);
        // Validate all subsequent meter readings that might be affected
        await _validateSubsequentReadings(topUp.date);
      }
    } catch (e) {
      logger.e('Error validating after top-up update', details: e.toString());
    }
  }

  /// Trigger validation after deleting a top-up
  Future<void> validateAfterTopUpDelete(DateTime deletedTopUpDate) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      // Validate all subsequent meter readings that might be affected
      await _validateSubsequentReadings(deletedTopUpDate);
    } catch (e) {
      logger.e('Error validating after top-up delete', details: e.toString());
    }
  }

  /// Trigger validation after entry conversion
  Future<void> validateAfterEntryConversion(DateTime conversionDate) async {
    // Skip validation during bulk operations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      // Validate all subsequent meter readings that might be affected
      await _validateSubsequentReadings(conversionDate);
    } catch (e) {
      logger.e('Error validating after entry conversion',
          details: e.toString());
    }
  }

  /// Validate a single meter reading and update its status
  Future<void> _validateAndUpdateSingleReading(
      MeterReading meterReading) async {
    try {
      final issues =
          await _dataIntegrityService.validateSingleEntry(meterReading);
      final shouldBeValid = issues.isEmpty;

      // Update status if it doesn't match the validation result
      final expectedStatus =
          shouldBeValid ? EntryStatus.valid : EntryStatus.invalid;
      if (meterReading.status != expectedStatus) {
        final updatedReading = meterReading.copyWith(status: expectedStatus);
        await _meterReadingRepository.updateMeterReading(updatedReading);
      }
    } catch (e) {
      logger.e('Error validating single reading', details: e.toString());
    }
  }

  /// Validate readings that might be affected by changes to the given reading
  Future<void> _validateAffectedReadings(MeterReading changedReading) async {
    try {
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final sortedReadings = List<MeterReading>.from(allReadings)
        ..sort((a, b) => a.date.compareTo(b.date));

      final changedIndex =
          sortedReadings.indexWhere((r) => r.id == changedReading.id);

      // Validate the next reading if it exists
      if (changedIndex >= 0 && changedIndex < sortedReadings.length - 1) {
        final nextReading = sortedReadings[changedIndex + 1];
        await _validateAndUpdateSingleReading(nextReading);
      }
    } catch (e) {
      logger.e('Error validating affected readings', details: e.toString());
    }
  }

  /// Validate a single top-up and update its status
  Future<void> _validateAndUpdateSingleTopUp(TopUp topUp) async {
    try {
      final isValid = await _validator.validateTopUp(topUp);

      // Update status if it doesn't match the validation result
      final expectedStatus = isValid ? EntryStatus.valid : EntryStatus.invalid;
      if (topUp.status != expectedStatus) {
        final updatedTopUp = topUp.copyWith(status: expectedStatus);
        await _topUpRepository.updateTopUp(updatedTopUp);
      }
    } catch (e) {
      logger.e('Error validating single top-up', details: e.toString());
    }
  }

  /// Validate all meter readings that occur after the given date
  Future<void> _validateSubsequentReadings(DateTime afterDate) async {
    try {
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final subsequentReadings = allReadings
          .where((reading) => reading.date.isAfter(afterDate))
          .toList();

      // Sort by date to validate in chronological order
      subsequentReadings.sort((a, b) => a.date.compareTo(b.date));

      // Validate each subsequent reading
      for (final reading in subsequentReadings) {
        await _validateAndUpdateSingleReading(reading);
      }
    } catch (e) {
      logger.e('Error validating subsequent readings', details: e.toString());
    }
  }

  /// Clean up surplus dismissal entries during validation cycle
  Future<void> _cleanupSurplusDismissalEntries() async {
    try {
      final dismissedEntryService = serviceLocator<DismissedEntryService>();
      await dismissedEntryService.cleanupInvalidDismissalEntries();
    } catch (e) {
      logger.e('Error during dismissal cleanup', details: e.toString());
    }
  }
}
