import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../models/validation_issue.dart';
import 'data_integrity_service.dart';

/// Unified service for all dismissed entry operations
class DismissedEntryService {
  final MeterReadingRepository _meterReadingRepository;

  /// Constructor
  DismissedEntryService(this._meterReadingRepository);

  /// Create a dismissal entry for a missing entry issue
  Future<MeterReading> createDismissalEntry(ValidationIssue issue) async {
    try {
      final startDate = DateTime.parse(issue.metadata!['start_date']);
      final endDate = DateTime.parse(issue.metadata!['end_date']);
      final gapDays = issue.metadata!['gap_days'] as int;

      Logger.info(
          'DismissedEntryService: Creating dismissal entry for gap from $startDate to $endDate ($gapDays days)');

      // Create single dismissal entry 62 days into the gap
      final dismissalDate = startDate.add(const Duration(days: 62));

      // Ensure dismissal date is before end date
      if (dismissalDate.isAfter(endDate) ||
          dismissalDate.isAtSameMomentAs(endDate)) {
        Logger.error(
            'DismissedEntryService: Cannot create dismissal entry - gap too short');
        throw Exception('Gap too short for dismissal entry');
      }

      final dismissalEntry = MeterReading(
        value: 0.0, // Zero value for dismissal entries
        date: dismissalDate,
        status: EntryStatus.ignored,
        notes:
            'Dismissed missing entry gap: $gapDays days (${_formatDate(startDate)} - ${_formatDate(endDate)})',
      );

      // Save the dismissal entry
      final id = await _meterReadingRepository.addMeterReading(dismissalEntry);
      Logger.info(
          'DismissedEntryService: Created dismissal entry with ID $id for date $dismissalDate');

      // Trigger validation recalculation to clear false positives
      try {
        final dataIntegrityService = serviceLocator<DataIntegrityService>();
        await dataIntegrityService.recalculateValidationAfterDismissal();
      } catch (e) {
        Logger.error(
            'DismissedEntryService: Error triggering validation recalculation: $e');
      }

      return dismissalEntry.copyWith(id: id);
    } catch (e) {
      Logger.error('DismissedEntryService: Error creating dismissal entry: $e');
      rethrow;
    }
  }

  /// Check if a gap period has been dismissed
  bool isGapDismissed(
      List<MeterReading> readings, DateTime start, DateTime end) {
    return readings.any((reading) =>
        isDismissedEntry(reading) &&
        reading.date.isAfter(start) &&
        reading.date.isBefore(end));
  }

  /// Check if an entry is a dismissed entry using unified criteria
  bool isDismissedEntry(MeterReading reading) {
    return reading.status == EntryStatus.ignored &&
        reading.value == 0.0 &&
        reading.notes != null &&
        reading.notes!.contains('Dismissed missing entry gap');
  }

  /// Clean up invalid dismissal entries
  Future<void> cleanupInvalidDismissalEntries() async {
    try {
      Logger.info(
          'DismissedEntryService: Starting cleanup of invalid dismissal entries');

      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final dismissalEntries = allReadings.where(isDismissedEntry).toList();

      if (dismissalEntries.isEmpty) {
        Logger.info(
            'DismissedEntryService: No dismissal entries found to clean up');
        return;
      }

      // Find orphaned dismissal entries (those not in valid gaps)
      final realEntries = allReadings
          .where((reading) => !isDismissedEntry(reading))
          .map((reading) => reading.date)
          .toList()
        ..sort();

      final orphanedEntries = <MeterReading>[];
      for (final dismissalEntry in dismissalEntries) {
        if (_isOrphanedDismissalEntry(dismissalEntry, realEntries)) {
          orphanedEntries.add(dismissalEntry);
        }
      }

      // Remove orphaned entries
      for (final entry in orphanedEntries) {
        if (entry.id != null) {
          await _meterReadingRepository.deleteMeterReading(entry.id!);
          Logger.info(
              'DismissedEntryService: Removed orphaned dismissal entry ${entry.id}');
        }
      }

      Logger.info(
          'DismissedEntryService: Cleanup completed, removed ${orphanedEntries.length} orphaned entries');
    } catch (e) {
      Logger.error('DismissedEntryService: Error during cleanup: $e');
    }
  }

  /// Check if a dismissal entry is orphaned (not in a valid gap)
  bool _isOrphanedDismissalEntry(
      MeterReading dismissalEntry, List<DateTime> realEntries) {
    // Find the gap that this dismissal entry should be in
    DateTime? previousEntry;
    DateTime? nextEntry;

    for (int i = 0; i < realEntries.length; i++) {
      if (realEntries[i].isAfter(dismissalEntry.date)) {
        nextEntry = realEntries[i];
        if (i > 0) {
          previousEntry = realEntries[i - 1];
        }
        break;
      }
    }

    // If we can't find the gap context, consider it orphaned
    if (previousEntry == null || nextEntry == null) {
      return true;
    }

    // Check if the gap is still >62 days (still needs dismissal)
    final gap = nextEntry.difference(previousEntry).inDays;
    return gap <= 62;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
