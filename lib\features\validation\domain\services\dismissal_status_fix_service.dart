import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../shared/domain/models/entry_status.dart';
import '../../../core/utils/logger.dart';

/// Service to fix existing dismissed entries that may not have correct status
class DismissalStatusFixService {
  final MeterReadingRepository _meterReadingRepository;

  DismissalStatusFixService(this._meterReadingRepository);

  /// Fix existing dismissed entries to have correct status
  Future<void> fixDismissedEntryStatuses() async {
    try {
      Logger.info('DismissalStatusFixService: Starting fix of dismissed entry statuses');
      
      // Get all meter readings
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      
      // Find entries that look like dismissed entries but don't have correct status
      final entriesToFix = allReadings.where((reading) {
        // Look for entries that have dismissal-related notes but wrong status
        final hasValue = reading.value != null && reading.value! > 0;
        final hasNotes = reading.notes != null;
        final hasDismissalNotes = hasNotes && 
            (reading.notes!.contains('Dismissed missing entry gap') ||
             reading.notes!.contains('dismissed') ||
             reading.notes!.toLowerCase().contains('gap'));
        final hasZeroValue = reading.value == null || reading.value == 0.0;
        
        // Entry should be marked as ignored if it has dismissal notes and zero value
        return (hasDismissalNotes && hasZeroValue && reading.status != EntryStatus.ignored);
      }).toList();

      if (entriesToFix.isEmpty) {
        Logger.info('DismissalStatusFixService: No dismissed entries need status fixing');
        return;
      }

      Logger.info('DismissalStatusFixService: Found ${entriesToFix.length} dismissed entries to fix');

      // Fix each entry
      for (final entry in entriesToFix) {
        final updatedEntry = entry.copyWith(status: EntryStatus.ignored);
        await _meterReadingRepository.updateMeterReading(updatedEntry);
        Logger.info('DismissalStatusFixService: Fixed entry ID ${entry.id} - set status to ignored');
      }

      Logger.info('DismissalStatusFixService: Successfully fixed ${entriesToFix.length} dismissed entry statuses');
      
    } catch (e) {
      Logger.error('DismissalStatusFixService: Error fixing dismissed entry statuses: $e');
      rethrow;
    }
  }
}
